class TikTokMonitorApp {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.clients = [];
        this.protoHelper = null;
        
        this.initializeElements();
        this.bindEvents();
        this.initializeProtobuf();
    }

    initializeElements() {
        // 连接状态相关
        this.connectionStatus = document.getElementById('connection-status');
        this.connectBtn = document.getElementById('connect-btn');
        
        // 客户端管理相关
        this.refreshClientsBtn = document.getElementById('refresh-clients');
        this.clientsList = document.getElementById('clients-list');
        this.clientsCount = document.getElementById('clients-count');
        this.clientSelect = document.getElementById('client-select');
        
        // 直播间控制相关
        this.liveUrlInput = document.getElementById('live-url');
        this.connectRoomBtn = document.getElementById('connect-room');
        this.disconnectRoomBtn = document.getElementById('disconnect-room');
        this.getStatusBtn = document.getElementById('get-status');
        
        // 广播消息相关
        this.broadcastMessageInput = document.getElementById('broadcast-message');
        this.sendBroadcastBtn = document.getElementById('send-broadcast');
        
        // 实时数据相关
        this.liveData = document.getElementById('live-data');
        this.clearLogsBtn = document.getElementById('clear-logs');
        this.autoScrollCheckbox = document.getElementById('auto-scroll');
        
        // 模态框相关
        this.modal = document.getElementById('modal');
        this.modalTitle = document.getElementById('modal-title');
        this.modalMessage = document.getElementById('modal-message');
        this.modalOk = document.getElementById('modal-ok');
        this.modalClose = document.querySelector('.close');
    }

    bindEvents() {
        // 连接按钮
        this.connectBtn.addEventListener('click', () => {
            if (this.isConnected) {
                this.disconnect();
            } else {
                this.connect();
            }
        });

        // 刷新客户端列表
        this.refreshClientsBtn.addEventListener('click', () => {
            this.refreshClients();
        });

        // 直播间控制
        this.connectRoomBtn.addEventListener('click', () => {
            this.connectToRoom();
        });

        this.disconnectRoomBtn.addEventListener('click', () => {
            this.disconnectFromRoom();
        });

        this.getStatusBtn.addEventListener('click', () => {
            this.getClientStatus();
        });

        // 广播消息
        this.sendBroadcastBtn.addEventListener('click', () => {
            this.sendBroadcast();
        });

        // 清空日志
        this.clearLogsBtn.addEventListener('click', () => {
            this.clearLogs();
        });

        // 模态框
        this.modalOk.addEventListener('click', () => {
            this.hideModal();
        });

        this.modalClose.addEventListener('click', () => {
            this.hideModal();
        });

        // 回车键发送
        this.broadcastMessageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendBroadcast();
            }
        });

        this.liveUrlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.connectToRoom();
            }
        });
    }

    initializeProtobuf() {
        // 这里应该初始化protobuf，但由于复杂性，我们先使用JSON格式
        this.addLog('system', '初始化protobuf支持...');
    }

    connect() {
        if (this.isConnected) return;

        this.updateConnectionStatus('connecting');
        this.addLog('system', '正在连接到WebSocket服务器...');

        try {
            this.ws = new WebSocket('ws://localhost:8087');
            
            this.ws.onopen = () => {
                this.isConnected = true;
                this.updateConnectionStatus('connected');
                this.addLog('system', '✅ 已连接到WebSocket服务器');
                this.refreshClients();
            };

            this.ws.onmessage = (event) => {
                this.handleMessage(event.data);
            };

            this.ws.onclose = () => {
                this.isConnected = false;
                this.updateConnectionStatus('disconnected');
                this.addLog('system', '❌ WebSocket连接已断开');
            };

            this.ws.onerror = (error) => {
                this.addLog('error', `WebSocket错误: ${error.message || '连接失败'}`);
                this.updateConnectionStatus('disconnected');
            };

        } catch (error) {
            this.addLog('error', `连接失败: ${error.message}`);
            this.updateConnectionStatus('disconnected');
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
        this.updateConnectionStatus('disconnected');
        this.addLog('system', '已断开WebSocket连接');
    }

    updateConnectionStatus(status) {
        this.connectionStatus.className = `status-${status}`;
        
        switch (status) {
            case 'connected':
                this.connectionStatus.textContent = '已连接';
                this.connectBtn.textContent = '断开连接';
                this.connectBtn.className = 'btn btn-danger';
                break;
            case 'connecting':
                this.connectionStatus.textContent = '连接中...';
                this.connectBtn.textContent = '连接中...';
                this.connectBtn.disabled = true;
                break;
            case 'disconnected':
                this.connectionStatus.textContent = '未连接';
                this.connectBtn.textContent = '连接服务器';
                this.connectBtn.className = 'btn btn-primary';
                this.connectBtn.disabled = false;
                break;
        }
    }

    handleMessage(data) {
        try {
            // 尝试解析为JSON（简化版本）
            if (typeof data === 'string') {
                const message = JSON.parse(data);
                this.processMessage(message);
            } else {
                // 这里应该处理protobuf二进制数据
                this.addLog('system', '收到protobuf消息（暂未完全支持）');
            }
        } catch (error) {
            this.addLog('error', `消息解析错误: ${error.message}`);
        }
    }

    processMessage(message) {
        switch (message.type) {
            case 'welcome':
                this.addLog('system', `服务器欢迎消息: ${message.message}`);
                break;
            case 'live_events':
                this.handleLiveEvents(message);
                break;
            case 'server_broadcast':
                this.addLog('system', `📢 服务器广播: ${message.message}`);
                break;
            case 'error':
                this.addLog('error', `服务器错误: ${message.message}`);
                break;
            default:
                this.addLog('system', `收到消息: ${JSON.stringify(message)}`);
        }
    }

    handleLiveEvents(message) {
        const data = message.data;
        if (data && data.events_data) {
            data.events_data.forEach(event => {
                this.processLiveEvent(event, message.liveUrl);
            });
        }
    }

    processLiveEvent(event, liveUrl) {
        const timestamp = new Date().toLocaleTimeString();
        
        switch (event.msg_type) {
            case 1: // 点赞
                if (event.like_msg) {
                    this.addLog('live', `👍 ${this.formatUser(event.like_msg.user)} 点赞x${event.like_msg.like_count}`);
                }
                break;
            case 2: // 评论
                if (event.comment_msg) {
                    this.addLog('live', `💬 ${this.formatUser(event.comment_msg.user)}: ${event.comment_msg.content}`);
                }
                break;
            case 3: // 礼物
                if (event.gift_msg) {
                    const gift = event.gift_msg;
                    this.addLog('live', `🎁 ${this.formatUser(gift.user)} 送出 ${gift.gift_name} x${gift.count}`);
                }
                break;
            case 4: // 进入
                if (event.member_msg) {
                    this.addLog('live', `👋 ${this.formatUser(event.member_msg.user)} 进入直播间`);
                }
                break;
            case 5: // 直播控制消息
                if (event.control_msg) {
                    this.handleControlMessage(event.control_msg);
                }
                break;
        }
    }

    handleControlMessage(control) {
        switch (control.action) {
            case 0:
                this.addLog('live', `🔴 ${control.room_name} 正在直播`);
                break;
            case 1:
                this.addLog('live', `⏹️ 直播结束`);
                break;
            case 100:
                this.addLog('error', `❌ URL错误: ${control.live_url}`);
                break;
            case 101:
                this.addLog('error', `❌ 其他错误: ${control.action_msg}`);
                break;
        }
    }

    formatUser(user) {
        if (!user) return '未知用户';
        let name = user.nick_name || user.user_name || '未知用户';
        if (user.level && user.level > 0) {
            name = `Lv.${user.level} ${name}`;
        }
        return name;
    }

    async refreshClients() {
        try {
            const response = await fetch('/api/clients');
            const clients = await response.json();
            this.clients = clients;
            this.updateClientsList();
            this.updateClientsCount();
            this.updateClientSelect();
        } catch (error) {
            this.addLog('error', `获取客户端列表失败: ${error.message}`);
        }
    }

    updateClientsList() {
        if (this.clients.length === 0) {
            this.clientsList.innerHTML = '<div class="no-clients">暂无连接的客户端</div>';
            return;
        }

        const html = this.clients.map(client => `
            <div class="client-item">
                <div class="client-id">客户端ID: ${client.id}</div>
                <div class="client-info">
                    上下文: ${client.context || '未知'} | 
                    连接数: ${client.totalConnections} | 
                    连接时间: ${new Date(client.connectedAt).toLocaleString()}
                </div>
            </div>
        `).join('');

        this.clientsList.innerHTML = html;
    }

    updateClientsCount() {
        this.clientsCount.textContent = `客户端数量: ${this.clients.length}`;
    }

    updateClientSelect() {
        const options = ['<option value="">请选择客户端</option>'];
        this.clients.forEach(client => {
            options.push(`<option value="${client.id}">${client.id} (${client.context || '未知'})</option>`);
        });
        this.clientSelect.innerHTML = options.join('');
    }

    async connectToRoom() {
        const clientId = this.clientSelect.value;
        const liveUrl = this.liveUrlInput.value.trim();

        if (!clientId) {
            this.showModal('错误', '请选择一个客户端');
            return;
        }

        if (!liveUrl) {
            this.showModal('错误', '请输入直播间URL');
            return;
        }

        try {
            const response = await fetch('/api/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId, liveUrl })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('client', `📡 已请求客户端 ${clientId} 连接到 ${liveUrl}`);
                this.showModal('成功', '连接请求已发送');
            } else {
                this.addLog('error', `连接请求失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `连接请求失败: ${error.message}`);
            this.showModal('错误', `连接请求失败: ${error.message}`);
        }
    }

    async disconnectFromRoom() {
        const clientId = this.clientSelect.value;

        if (!clientId) {
            this.showModal('错误', '请选择一个客户端');
            return;
        }

        try {
            const response = await fetch('/api/disconnect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('client', `📡 已请求客户端 ${clientId} 断开连接`);
                this.showModal('成功', '断开请求已发送');
            } else {
                this.addLog('error', `断开请求失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `断开请求失败: ${error.message}`);
            this.showModal('错误', `断开请求失败: ${error.message}`);
        }
    }

    async getClientStatus() {
        const clientId = this.clientSelect.value;

        if (!clientId) {
            this.showModal('错误', '请选择一个客户端');
            return;
        }

        try {
            const response = await fetch('/api/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('client', `📊 已请求客户端 ${clientId} 状态信息`);
                this.showModal('成功', '状态请求已发送');
            } else {
                this.addLog('error', `状态请求失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `状态请求失败: ${error.message}`);
            this.showModal('错误', `状态请求失败: ${error.message}`);
        }
    }

    async sendBroadcast() {
        const message = this.broadcastMessageInput.value.trim();

        if (!message) {
            this.showModal('错误', '请输入广播消息');
            return;
        }

        try {
            const response = await fetch('/api/broadcast', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('system', `📢 广播消息已发送: ${message}`);
                this.broadcastMessageInput.value = '';
                this.showModal('成功', '广播消息已发送');
            } else {
                this.addLog('error', `广播失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `广播失败: ${error.message}`);
            this.showModal('错误', `广播失败: ${error.message}`);
        }
    }

    addLog(type, message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;

        logEntry.innerHTML = `
            <span class="timestamp">[${timestamp}]</span>
            <span class="message">${message}</span>
        `;

        this.liveData.appendChild(logEntry);

        // 自动滚动
        if (this.autoScrollCheckbox.checked) {
            this.liveData.scrollTop = this.liveData.scrollHeight;
        }

        // 限制日志条数
        const logs = this.liveData.querySelectorAll('.log-entry');
        if (logs.length > 1000) {
            logs[0].remove();
        }
    }

    clearLogs() {
        this.liveData.innerHTML = '';
        this.addLog('system', '日志已清空');
    }

    showModal(title, message) {
        this.modalTitle.textContent = title;
        this.modalMessage.textContent = message;
        this.modal.style.display = 'block';
    }

    hideModal() {
        this.modal.style.display = 'none';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TikTokMonitorApp();
});

// 点击模态框外部关闭
window.addEventListener('click', (event) => {
    const modal = document.getElementById('modal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
});
