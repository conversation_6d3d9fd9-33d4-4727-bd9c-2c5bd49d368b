<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok直播监控工具 - 控制面板</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎯 TikTok直播监控工具</h1>
            <div class="connection-status">
                <span id="connection-status" class="status-disconnected">未连接</span>
                <button id="connect-btn" class="btn btn-primary">连接服务器</button>
            </div>
        </header>

        <main>
            <!-- 客户端管理区域 -->
            <section class="panel">
                <h2>📱 客户端管理</h2>
                <div class="clients-container">
                    <div class="clients-header">
                        <button id="refresh-clients" class="btn btn-secondary">刷新客户端列表</button>
                        <span id="clients-count">客户端数量: 0</span>
                    </div>
                    <div id="clients-list" class="clients-list">
                        <div class="no-clients">暂无连接的客户端</div>
                    </div>
                </div>
            </section>

            <!-- 直播间控制区域 -->
            <section class="panel">
                <h2>🎬 直播间控制</h2>
                <div class="control-form">
                    <div class="form-group">
                        <label for="client-select">选择客户端:</label>
                        <select id="client-select" class="form-control">
                            <option value="">请选择客户端</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="live-url">直播间URL:</label>
                        <input type="text" id="live-url" class="form-control" 
                               placeholder="https://www.tiktok.com/@username/live">
                    </div>
                    <div class="form-actions">
                        <button id="connect-room" class="btn btn-success">连接直播间</button>
                        <button id="disconnect-room" class="btn btn-danger">断开直播间</button>
                        <button id="get-status" class="btn btn-info">获取状态</button>
                    </div>
                </div>
            </section>

            <!-- 窗口控制区域 -->
            <section class="panel">
                <h2>🪟 窗口控制</h2>
                <div class="window-control-form">
                    <div class="form-group">
                        <label for="connection-select">选择连接:</label>
                        <select id="connection-select" class="form-control">
                            <option value="">请选择活跃连接</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button id="show-window" class="btn btn-success">显示窗口</button>
                        <button id="hide-window" class="btn btn-warning">隐藏窗口</button>
                        <button id="refresh-connections" class="btn btn-secondary">刷新连接</button>
                    </div>
                </div>
            </section>

            <!-- 连接历史区域 -->
            <section class="panel">
                <h2>📋 连接历史</h2>
                <div class="history-container">
                    <div class="history-header">
                        <button id="clear-history" class="btn btn-secondary">清空历史</button>
                        <span id="history-count">历史记录: 0</span>
                    </div>
                    <div id="connection-history" class="connection-history">
                        <div class="no-history">暂无连接历史</div>
                    </div>
                </div>
            </section>

            <!-- 广播消息区域 -->
            <section class="panel">
                <h2>📢 广播消息</h2>
                <div class="broadcast-form">
                    <div class="form-group">
                        <label for="broadcast-message">广播消息:</label>
                        <input type="text" id="broadcast-message" class="form-control" 
                               placeholder="输入要广播的消息">
                    </div>
                    <button id="send-broadcast" class="btn btn-warning">发送广播</button>
                </div>
            </section>

            <!-- 实时数据展示区域 -->
            <section class="panel">
                <h2>📊 实时数据</h2>
                <div class="data-controls">
                    <button id="clear-logs" class="btn btn-secondary">清空日志</button>
                    <label class="checkbox-label">
                        <input type="checkbox" id="auto-scroll" checked>
                        自动滚动
                    </label>
                </div>
                <div id="live-data" class="live-data">
                    <div class="log-entry system">
                        <span class="timestamp">[系统]</span>
                        <span class="message">等待连接到服务器...</span>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 数据持久化指示器 -->
    <div id="persistence-indicator" class="persistence-indicator">
        数据已保存
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3 id="modal-title">提示</h3>
            <p id="modal-message"></p>
            <div class="modal-actions">
                <button id="modal-ok" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>

    <script src="protobuf.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
