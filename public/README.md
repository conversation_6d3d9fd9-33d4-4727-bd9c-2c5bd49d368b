# TikTok直播监控工具 - 前端界面

这是TikTok直播监控工具的Web前端界面，提供了一个直观的控制面板来管理直播间监控功能。

## 功能特性

### 🔗 连接管理
- 实时WebSocket连接状态显示
- 一键连接/断开服务器
- 连接状态指示器

### 📱 客户端管理
- 显示所有连接的客户端
- 实时更新客户端列表
- 显示客户端详细信息（ID、上下文、连接数、连接时间）

### 🎬 直播间控制
- 选择客户端连接到指定直播间
- 断开客户端的直播间连接
- 获取客户端状态信息
- 支持TikTok直播间URL格式

### 📢 广播消息
- 向所有连接的客户端发送广播消息
- 支持键盘快捷键（回车发送）

### 📊 实时数据展示
- 实时显示直播间事件
- 支持多种事件类型：
  - 👍 点赞事件
  - 💬 评论消息
  - 🎁 礼物赠送
  - 👋 用户进入
  - 🔴 直播状态变化
- 自动滚动功能
- 日志清空功能
- 彩色分类显示

### 🪟 窗口控制
- 显示/隐藏直播间监控窗口
- 选择特定连接进行窗口控制
- 实时连接状态指示器

### 💾 数据持久化
- 自动保存连接历史（最近50条）
- 保存活跃连接状态
- 保存日志记录（最近100条）
- 页面刷新后数据不丢失
- 定期自动保存（每30秒）

### 📋 连接历史
- 显示历史连接记录
- 包含客户端ID、URL、连接时间等信息
- 支持清空历史记录
- 最多保存100条历史记录

## 使用方法

### 1. 启动服务器
```bash
node src/remote-server-demo.js
```

服务器将启动两个端口：
- WebSocket服务器：`ws://localhost:8087`
- HTTP服务器：`http://localhost:3001`

### 2. 访问前端界面
在浏览器中打开：`http://localhost:3001`

### 3. 连接到服务器
1. 点击"连接服务器"按钮
2. 等待连接状态变为"已连接"
3. 客户端列表将自动刷新

### 4. 管理直播间
1. 在客户端列表中选择一个客户端
2. 输入TikTok直播间URL（格式：`https://www.tiktok.com/@username/live`）
3. 点击"连接直播间"开始监控
4. 实时数据将显示在底部的日志区域

### 5. 发送广播消息
1. 在广播消息输入框中输入消息
2. 点击"发送广播"或按回车键
3. 消息将发送给所有连接的客户端

### 6. 控制窗口显示
1. 在"窗口控制"区域选择一个活跃连接
2. 点击"显示窗口"或"隐藏窗口"
3. 对应的直播间监控窗口将显示或隐藏

### 7. 查看连接历史
1. 在"连接历史"区域查看所有历史连接
2. 包含客户端ID、直播间URL、连接时间等信息
3. 可以点击"清空历史"清除所有记录

## 界面说明

### 连接状态指示器
- 🟢 **已连接**：成功连接到WebSocket服务器
- 🟡 **连接中**：正在尝试连接
- 🔴 **未连接**：未连接或连接断开

### 日志颜色说明
- 🔵 **蓝色**：系统消息
- 🟢 **绿色**：客户端操作
- 🟡 **黄色**：直播事件
- 🔴 **红色**：错误消息

### 连接状态指示器
- 🟢 **绿色圆点**：连接活跃
- 🔴 **红色圆点**：连接断开
- ⚪ **白色圆点**：状态未知

### 数据持久化说明
- 数据自动保存到浏览器本地存储
- 页面刷新后数据不会丢失
- 右上角会显示"数据已保存"提示
- 每30秒自动保存一次
- 页面关闭时自动保存

### 支持的直播间URL格式
- `https://www.tiktok.com/@username/live`
- `https://tiktok.com/@username/live`

## 技术特性

- 响应式设计，支持移动设备
- 实时WebSocket通信
- Protobuf消息格式支持
- 现代化UI设计
- 自动日志管理（限制1000条）
- 错误处理和用户反馈

## 故障排除

### 连接失败
1. 确保服务器正在运行
2. 检查端口8087是否被占用
3. 确认防火墙设置

### 客户端列表为空
1. 确保有客户端连接到WebSocket服务器
2. 点击"刷新客户端列表"按钮
3. 检查服务器日志

### 直播数据不显示
1. 确认直播间URL格式正确
2. 检查直播间是否正在直播
3. 查看错误日志获取详细信息

## 开发说明

前端文件结构：
```
public/
├── index.html      # 主页面
├── style.css       # 样式文件
├── app.js          # 主要JavaScript逻辑
├── protobuf.min.js # Protobuf支持
└── README.md       # 说明文档
```

主要技术栈：
- HTML5 + CSS3
- 原生JavaScript (ES6+)
- WebSocket API
- Fetch API
- Protobuf.js
