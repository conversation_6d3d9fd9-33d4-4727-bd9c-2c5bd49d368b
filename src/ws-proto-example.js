/**
 * WebSocket Protobuf 使用示例
 * 展示如何使用生成的protobuf消息类型
 */

// 导入生成的protobuf代码
const protobuf = require('./auto_gen_webcast');

class WebSocketProtoHelper {
    constructor() {
        // 获取protobuf消息类型
        this.ClientToServerMessage = protobuf.ClientToServerMessage;
        this.ServerToClientMessage = protobuf.ServerToClientMessage;
        
        // 客户端消息类型
        this.ClientInfoMessage = protobuf.ClientInfoMessage;
        this.HeartbeatMessage = protobuf.HeartbeatMessage;
        this.LiveRoomResponseMessage = protobuf.LiveRoomResponseMessage;
        this.StatusResponseMessage = protobuf.StatusResponseMessage;
        this.LiveEventsMessage = protobuf.LiveEventsMessage;
        this.ErrorMessage = protobuf.ErrorMessage;
        this.ClientShutdownMessage = protobuf.ClientShutdownMessage;
        this.CustomMessage = protobuf.CustomMessage;
        this.ShowWindowResponseMessage = protobuf.ShowWindowResponseMessage;
        this.DisconnectRoomResponseMessage = protobuf.DisconnectRoomResponseMessage;
        
        // 服务器消息类型
        this.WelcomeMessage = protobuf.WelcomeMessage;
        this.LiveRoomRequestMessage = protobuf.LiveRoomRequestMessage;
        this.DisconnectRoomRequestMessage = protobuf.DisconnectRoomRequestMessage;
        this.GetStatusRequestMessage = protobuf.GetStatusRequestMessage;
        this.ServerBroadcastMessage = protobuf.ServerBroadcastMessage;
        this.ServerErrorMessage = protobuf.ServerErrorMessage;
        this.ShowWindowRequestMessage = protobuf.ShowWindowRequestMessage;
    }

    // ============================================================================
    // 客户端消息创建方法
    // ============================================================================

    /**
     * 创建客户端信息消息
     */
    createClientInfoMessage(context) {
        const message = this.ClientInfoMessage.create({
            type: 'client_info',
            context: context,
            timestamp: Date.now()
        });
        
        return this.ClientToServerMessage.create({
            clientInfo: message
        });
    }

    /**
     * 创建心跳消息
     */
    createHeartbeatMessage(context, totalConnections) {
        const message = this.HeartbeatMessage.create({
            type: 'heartbeat',
            context: context,
            totalConnections: totalConnections,
            timestamp: Date.now()
        });
        
        return this.ClientToServerMessage.create({
            heartbeat: message
        });
    }

    /**
     * 创建直播间响应消息
     */
    createLiveRoomResponseMessage(requestId, success, connectionId, liveUrl, error = null) {
        const message = this.LiveRoomResponseMessage.create({
            type: 'live_room_response',
            requestId: requestId,
            success: success,
            connectionId: connectionId,
            liveUrl: liveUrl,
            error: error,
            timestamp: Date.now()
        });
        
        return this.ClientToServerMessage.create({
            liveRoomResponse: message
        });
    }

    /**
     * 创建状态响应消息
     */
    createStatusResponseMessage(requestId, statusInfo) {
        const message = this.StatusResponseMessage.create({
            type: 'status_response',
            requestId: requestId,
            status: statusInfo,
            timestamp: Date.now()
        });
        
        return this.ClientToServerMessage.create({
            statusResponse: message
        });
    }

    /**
     * 创建直播事件消息
     */
    createLiveEventsMessage(connectionId, liveUrl, eventsData, context) {
        const message = this.LiveEventsMessage.create({
            type: 'live_events',
            connectionId: connectionId,
            liveUrl: liveUrl,
            data: JSON.stringify(eventsData),
            timestamp: Date.now(),
            source: 'LiveTiktok',
            context: context
        });
        
        return this.ClientToServerMessage.create({
            liveEvents: message
        });
    }

    /**
     * 创建错误消息
     */
    createErrorMessage(error, connectionId = null, liveUrl = null, context = null) {
        const message = this.ErrorMessage.create({
            type: 'error',
            connectionId: connectionId,
            liveUrl: liveUrl,
            error: error,
            timestamp: Date.now(),
            context: context
        });
        
        return this.ClientToServerMessage.create({
            error: message
        });
    }

    /**
     * 创建客户端关闭消息
     */
    createClientShutdownMessage(shutdownMessage, totalConnections, context) {
        const message = this.ClientShutdownMessage.create({
            type: 'client_shutdown',
            message: shutdownMessage,
            totalConnections: totalConnections,
            timestamp: Date.now(),
            context: context
        });
        
        return this.ClientToServerMessage.create({
            clientShutdown: message
        });
    }

    /**
     * 创建自定义消息
     */
    createCustomMessage(data, context) {
        const message = this.CustomMessage.create({
            type: 'custom',
            data: JSON.stringify(data),
            timestamp: Date.now(),
            source: 'bridge',
            context: context
        });

        return this.ClientToServerMessage.create({
            custom: message
        });
    }

    /**
     * 创建显示窗口响应消息
     */
    createShowWindowResponseMessage(requestId, success, connectionId, error = null) {
        const message = this.ShowWindowResponseMessage.create({
            type: 'show_window_response',
            requestId: requestId,
            success: success,
            connectionId: connectionId,
            error: error,
            timestamp: Date.now()
        });

        return this.ClientToServerMessage.create({
            showWindowResponse: message
        });
    }

    /**
     * 创建断开连接响应消息
     */
    createDisconnectRoomResponseMessage(requestId, success, connectionId, error = null) {
        const message = this.DisconnectRoomResponseMessage.create({
            type: 'disconnect_room_response',
            requestId: requestId,
            success: success,
            connectionId: connectionId,
            error: error,
            timestamp: Date.now()
        });

        return this.ClientToServerMessage.create({
            disconnectRoomResponse: message
        });
    }

    // ============================================================================
    // 服务器消息创建方法
    // ============================================================================

    /**
     * 创建欢迎消息
     */
    createWelcomeMessage(welcomeMessage, clientId) {
        const message = this.WelcomeMessage.create({
            type: 'welcome',
            message: welcomeMessage,
            clientId: clientId,
            timestamp: Date.now()
        });
        
        return this.ServerToClientMessage.create({
            welcome: message
        });
    }

    /**
     * 创建直播间请求消息
     */
    createLiveRoomRequestMessage(liveUrl, requestId, options = null) {
        const message = this.LiveRoomRequestMessage.create({
            type: 'live_room_request',
            liveUrl: liveUrl,
            requestId: requestId,
            options: options ? JSON.stringify(options) : null,
            timestamp: Date.now()
        });
        
        return this.ServerToClientMessage.create({
            liveRoomRequest: message
        });
    }

    /**
     * 创建断开直播间请求消息
     */
    createDisconnectRoomRequestMessage(connectionId, requestId) {
        const message = this.DisconnectRoomRequestMessage.create({
            type: 'disconnect_room_request',
            connectionId: connectionId,
            requestId: requestId,
            timestamp: Date.now()
        });
        
        return this.ServerToClientMessage.create({
            disconnectRoomRequest: message
        });
    }

    /**
     * 创建获取状态请求消息
     */
    createGetStatusRequestMessage(requestId) {
        const message = this.GetStatusRequestMessage.create({
            type: 'get_status_request',
            requestId: requestId,
            timestamp: Date.now()
        });
        
        return this.ServerToClientMessage.create({
            getStatusRequest: message
        });
    }

    /**
     * 创建服务器广播消息
     */
    createServerBroadcastMessage(broadcastMessage) {
        const message = this.ServerBroadcastMessage.create({
            type: 'server_broadcast',
            message: broadcastMessage,
            timestamp: Date.now()
        });

        return this.ServerToClientMessage.create({
            serverBroadcast: message
        });
    }

    /**
     * 创建服务器错误消息
     */
    createServerErrorMessage(errorMessage) {
        const message = this.ServerErrorMessage.create({
            type: 'error',
            message: errorMessage,
            timestamp: Date.now()
        });

        return this.ServerToClientMessage.create({
            serverError: message
        });
    }

    /**
     * 创建显示窗口请求消息
     */
    createShowWindowRequestMessage(connectionId, requestId, show = true) {
        const message = this.ShowWindowRequestMessage.create({
            type: 'show_window_request',
            connectionId: connectionId,
            requestId: requestId,
            show: show,
            timestamp: Date.now()
        });

        return this.ServerToClientMessage.create({
            showWindowRequest: message
        });
    }

    // ============================================================================
    // 消息序列化和反序列化方法
    // ============================================================================

    /**
     * 序列化客户端消息为二进制数据
     */
    serializeClientMessage(message) {
        return this.ClientToServerMessage.encode(message).finish();
    }

    /**
     * 序列化服务器消息为二进制数据
     */
    serializeServerMessage(message) {
        return this.ServerToClientMessage.encode(message).finish();
    }

    /**
     * 反序列化客户端消息
     */
    deserializeClientMessage(buffer) {
        return this.ClientToServerMessage.decode(buffer);
    }

    /**
     * 反序列化服务器消息
     */
    deserializeServerMessage(buffer) {
        return this.ServerToClientMessage.decode(buffer);
    }

    /**
     * 获取消息类型字符串
     */
    getMessageType(message) {
        if (message.clientInfo) return 'client_info';
        if (message.heartbeat) return 'heartbeat';
        if (message.liveRoomResponse) return 'live_room_response';
        if (message.statusResponse) return 'status_response';
        if (message.liveEvents) return 'live_events';
        if (message.error) return 'error';
        if (message.clientShutdown) return 'client_shutdown';
        if (message.custom) return 'custom';
        if (message.showWindowResponse) return 'show_window_response';
        if (message.disconnectRoomResponse) return 'disconnect_room_response';
        if (message.welcome) return 'welcome';
        if (message.liveRoomRequest) return 'live_room_request';
        if (message.disconnectRoomRequest) return 'disconnect_room_request';
        if (message.getStatusRequest) return 'get_status_request';
        if (message.serverBroadcast) return 'server_broadcast';
        if (message.serverError) return 'server_error';
        if (message.showWindowRequest) return 'show_window_request';
        return 'unknown';
    }
}

module.exports = WebSocketProtoHelper;
