const LiveTiktok = require('./tiktok');
const crypto = require('crypto');
const { BrowserWindow } = require('electron');
const {userInfo} = require("node:os");
const WebSocket = require('ws');
const WebSocketProtoHelper = require('./ws-proto-example');

// 1
// 窗口管理器类
class WindowManager {
    constructor() {
        this.windows = new Map(); // connectionId -> BrowserWindow
        this.windowOptions = {
            width: 1024,
            height: 768,
            show: true, // 默认隐藏窗口，只用于数据监听
        };
    }

    // 创建新窗口
    createWindow(connectionId, liveUrl) {
        const window = new BrowserWindow({
            ...this.windowOptions,
            title: `LiveTiktok Monitor - ${liveUrl}`
        });

        // 存储窗口引用
        this.windows.set(connectionId, window);

        // 监听窗口关闭事件
        window.on('closed', () => {
            this.windows.delete(connectionId);
            console.log(`Window for connection ${connectionId} closed`);
        });

        // 加载空白页面
        window.loadURL('about:blank');

        console.log(`Created window for connection ${connectionId}: ${liveUrl}`);
        return window;
    }

    // 获取窗口
    getWindow(connectionId) {
        return this.windows.get(connectionId);
    }

    // 关闭窗口
    closeWindow(connectionId) {
        const window = this.windows.get(connectionId);
        if (window && !window.isDestroyed()) {
            window.close();
        }
        this.windows.delete(connectionId);
    }

    // 关闭所有窗口
    closeAllWindows() {
        for (const [connectionId, window] of this.windows) {
            if (!window.isDestroyed()) {
                window.close();
            }
        }
        this.windows.clear();
    }

    // 获取窗口数量
    getWindowCount() {
        return this.windows.size;
    }

    // 获取所有窗口信息
    getAllWindowsInfo() {
        const windowsInfo = [];
        for (const [connectionId, window] of this.windows) {
            if (!window.isDestroyed()) {
                windowsInfo.push({
                    connectionId: connectionId,
                    title: window.getTitle(),
                    isVisible: window.isVisible(),
                    isMinimized: window.isMinimized(),
                    bounds: window.getBounds()
                });
            }
        }
        return windowsInfo;
    }

    // 设置窗口可见性
    setWindowVisibility(connectionId, visible) {
        const window = this.windows.get(connectionId);
        if (window && !window.isDestroyed()) {
            if (visible) {
                window.show();
            } else {
                window.hide();
            }
        }
    }
}

class LiveWebSocketBridge {
    constructor(context = 'uuid-1234', wsPort = 8085, test = false) {
        this.context = context;
        this.wsPort = wsPort;
        this.test = test;

        // 初始化protobuf辅助类
        this.protoHelper = new WebSocketProtoHelper();

        // 创建WebSocket客户端（新模式）
        this.wsClient = null;
        this.serverUrl = `ws://localhost:${wsPort}`; // 默认连接地址
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        // 绑定客户端事件处理器
        this.setupClientEventHandlers();

        // 多个直播间连接管理
        this.connections = new Map(); // connectionId -> connection info
        this.windowManager = new WindowManager(); // 窗口管理器

        // 全局统计信息
        this.globalStats = {
            totalConnections: 0,
            activeConnections: 0,
            totalEvents: 0,
            eventsByType: {},
            startTime: null,
            lastEventTime: null
        };
    }

    // 生成连接ID
    generateConnectionId() {
        const username = userInfo().username;
        return username+'_'+crypto.randomBytes(8).toString('hex');
    }

    // 设置客户端模式的事件处理器
    setupClientEventHandlers() {
        // 客户端模式下的事件处理将在连接建立后设置
        console.log('LiveWebSocketBridge: Client mode event handlers setup');
    }

    // 启动WebSocket服务器
    startWebSocketServer() {
        console.warn('Cannot start WebSocket server in client mode');
    }

    // 停止WebSocket服务器
    stopWebSocketServer() {
        console.warn('Cannot stop WebSocket server in client mode');
    }

    // 设置远程服务器地址（客户端模式）
    setServerUrl(url) {
        this.serverUrl = url;
        console.log(`LiveWebSocketBridge: Server URL set to ${url}`);
    }

    // 连接到远程WebSocket服务器（客户端模式）
    connectToServer() {
        if (this.isConnected) {
            console.log('Already connected to server');
            return;
        }

        console.log(`Connecting to WebSocket server: ${this.serverUrl}`);

        const WebSocket = require('ws');
        this.wsClient = new WebSocket(this.serverUrl);

        this.wsClient.on('open', () => {
            console.log('✅ Connected to WebSocket server');
            this.isConnected = true;
            this.reconnectAttempts = 0;

            // 发送客户端信息
            this.sendClientInfo();
        });

        this.wsClient.on('message', (data) => {
            try {
                // 使用protobuf解析消息
                this.handleProtobufServerMessage(data);
            } catch (error) {
                console.error('Error parsing server message:', error);
            }
        });

        this.wsClient.on('close', () => {
            console.log('❌ Disconnected from WebSocket server');
            this.isConnected = false;
            this.wsClient = null;

            // 尝试重连
            this.attemptReconnect();
        });

        this.wsClient.on('error', (error) => {
            console.error('WebSocket client error:', error);
            this.isConnected = false;
        });
    }

    // 断开与远程服务器的连接（客户端模式）
    disconnectFromServer() {

        if (this.wsClient && this.isConnected) {
            this.wsClient.close();
            this.isConnected = false;
            console.log('Disconnected from WebSocket server');
        }
    }

    // 尝试重连到服务器
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            return;
        }

        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

        setTimeout(() => {
            this.connectToServer();
        }, this.reconnectDelay);
    }

    // 发送客户端信息
    sendClientInfo() {
        const message = this.protoHelper.createClientInfoMessage(this.context);
        const buffer = this.protoHelper.serializeClientMessage(message);
        this.wsClient.send(buffer);
    }

    // 发送消息到服务器（客户端模式）
    sendToServer(data) {
        if (!this.isConnected || !this.wsClient) {
            console.warn('Not connected to server, cannot send message');
            return;
        }

        try {
            // data应该已经是序列化的buffer
            this.wsClient.send(data);
        } catch (error) {
            console.error('Error sending message to server:', error);
        }
    }

    // 处理protobuf服务器消息
    handleProtobufServerMessage(data) {
        try {
            const message = this.protoHelper.deserializeServerMessage(data);
            const messageType = this.protoHelper.getMessageType(message);
            console.log('Received protobuf message from server:', messageType);

            switch (messageType) {
                case 'welcome':
                    console.log('Server welcome:', message.welcome.message);
                    break;
                case 'live_room_request':
                    this.handleProtobufLiveRoomRequest(message.liveRoomRequest);
                    break;
                case 'disconnect_room_request':
                    this.handleProtobufDisconnectRoomRequest(message.disconnectRoomRequest);
                    break;
                case 'get_status_request':
                    this.handleProtobufStatusRequest(message.getStatusRequest);
                    break;
                case 'show_window_request':
                    this.handleProtobufShowWindowRequest(message.showWindowRequest);
                    break;
                default:
                    console.log('Unknown protobuf message type from server:', messageType);
            }
        } catch (error) {
            console.error('Error handling protobuf server message:', error);
        }
    }

    // 处理protobuf版本的直播间连接请求
    async handleProtobufLiveRoomRequest(message) {
        const liveUrl = message.liveUrl;
        const options = message.options ? JSON.parse(message.options) : {};
        const requestId = message.requestId;

        try {
            console.log(`Server requested connection to live room: ${liveUrl}`);

            // 创建新连接
            const connectionId = this.generateConnectionId();
            await this.createLiveConnection(connectionId, liveUrl, options);

            // 发送成功响应给服务器
            const responseMessage = this.protoHelper.createLiveRoomResponseMessage(
                requestId, true, connectionId, liveUrl
            );
            const buffer = this.protoHelper.serializeClientMessage(responseMessage);
            this.sendToServer(buffer);

            console.log(`Successfully connected to live room: ${liveUrl} (ID: ${connectionId})`);

        } catch (error) {
            console.error('Error connecting to live room:', error);

            // 发送错误响应给服务器
            const errorMessage = this.protoHelper.createLiveRoomResponseMessage(
                requestId, false, null, liveUrl, error.message
            );
            const buffer = this.protoHelper.serializeClientMessage(errorMessage);
            this.sendToServer(buffer);
        }
    }

    // 处理protobuf版本的断开直播间请求
    handleProtobufDisconnectRoomRequest(message) {
        const connectionId = message.connectionId;
        const requestId = message.requestId;

        try {
            if (connectionId) {
                // 断开特定连接
                this.disconnectConnection(connectionId);
                console.log(`Disconnected from live room: ${connectionId}`);
            } else {
                // 断开所有连接
                this.disconnectAllConnections();
                console.log('Disconnected from all live rooms');
            }

            // 发送成功响应给服务器
            const successMessage = this.protoHelper.createDisconnectRoomResponseMessage(
                requestId, true, connectionId
            );
            const buffer = this.protoHelper.serializeClientMessage(successMessage);
            this.sendToServer(buffer);

        } catch (error) {
            console.error('Error disconnecting from live room:', error);

            // 发送错误响应给服务器
            const errorMessage = this.protoHelper.createDisconnectRoomResponseMessage(
                requestId, false, connectionId, error.message
            );
            const buffer = this.protoHelper.serializeClientMessage(errorMessage);
            this.sendToServer(buffer);
        }
    }

    // 处理protobuf版本的状态请求
    handleProtobufStatusRequest(message) {
        const requestId = message.requestId;

        const status = {
            context: this.context,
            isConnected: this.isConnected,
            totalConnections: this.connections.size,
            connections: Array.from(this.connections.entries()).map(([id, conn]) => ({
                id: id,
                liveUrl: conn.liveUrl,
                status: conn.liveTiktok ? 'active' : 'inactive',
                windowVisible: conn.window ? conn.window.isVisible() : false
            })),
            globalStats: this.globalStats,
            timestamp: new Date().getTime()
        };

        // 发送状态响应给服务器
        const message = this.protoHelper.createStatusResponseMessage(requestId, status);
        const buffer = this.protoHelper.serializeClientMessage(message);
        this.sendToServer(buffer);
    }

    // 处理protobuf版本的显示窗口请求
    handleProtobufShowWindowRequest(message) {
        const connectionId = message.connectionId;
        const requestId = message.requestId;
        const show = message.show;

        try {
            const connection = this.connections.get(connectionId);
            if (!connection) {
                // 发送错误响应
                const errorMessage = this.protoHelper.createShowWindowResponseMessage(
                    requestId, false, connectionId, `Connection ${connectionId} not found`
                );
                const buffer = this.protoHelper.serializeClientMessage(errorMessage);
                this.sendToServer(buffer);
                return;
            }

            // 显示或隐藏窗口
            if (connection.window && !connection.window.isDestroyed()) {
                if (show) {
                    connection.window.show();
                    connection.window.focus();
                    console.log(`Showing window for connection ${connectionId}`);
                } else {
                    connection.window.hide();
                    console.log(`Hiding window for connection ${connectionId}`);
                }

                // 发送成功响应
                const successMessage = this.protoHelper.createShowWindowResponseMessage(
                    requestId, true, connectionId, null, show
                );
                const buffer = this.protoHelper.serializeClientMessage(successMessage);
                this.sendToServer(buffer);
            } else {
                // 发送错误响应
                const errorMessage = this.protoHelper.createShowWindowResponseMessage(
                    requestId, false, connectionId, `Window for connection ${connectionId} not available`
                );
                const buffer = this.protoHelper.serializeClientMessage(errorMessage);
                this.sendToServer(buffer);
            }
        } catch (error) {
            console.error('Error handling show window request:', error);
            // 发送错误响应
            const errorMessage = this.protoHelper.createShowWindowResponseMessage(
                requestId, false, connectionId, error.message
            );
            const buffer = this.protoHelper.serializeClientMessage(errorMessage);
            this.sendToServer(buffer);
        }
    }







    // 创建直播间连接（为每个直播间创建独立窗口）
    async createLiveConnection(connectionId, liveUrl, options = {}) {
        // 为这个连接创建独立的窗口
        const window = this.windowManager.createWindow(connectionId, liveUrl);

        const connection = {
            id: connectionId,
            liveUrl: liveUrl,
            window: window,
            liveTiktok: new LiveTiktok(`${this.context}-${connectionId}`, this.test),
            isConnected: false,
            startTime: null,
            stats: {
                totalEvents: 0,
                eventsByType: {},
                lastEventTime: null
            },
            options: options
        };

        // 创建事件回调函数
        const eventCallback = (eventsData) => {
            try {
                // 更新连接统计信息
                this.updateConnectionStats(connectionId, eventsData);

                // 更新全局统计信息
                this.updateGlobalStats(eventsData);

                // 客户端模式：发送事件数据到远程服务器
                const message = this.protoHelper.createLiveEventsMessage(
                    connectionId, liveUrl, eventsData, this.context
                );
                const buffer = this.protoHelper.serializeClientMessage(message);
                this.sendToServer(buffer);

            } catch (error) {
                console.error(`Error in eventCallback for connection ${connectionId}:`, error);
                const errorMessage = this.protoHelper.createErrorMessage(
                    connectionId, liveUrl, error.message, this.context
                );
                const buffer = this.protoHelper.serializeClientMessage(errorMessage);
                this.sendToServer(buffer);
            }
        };

        // 创建日志回调函数
        const logCallback = (info) => {
            const logMessage = `[${connectionId}] ${liveUrl}: ${info}`;
            console.log(`LiveTiktok Log: ${logMessage}`);

            // 使用CustomMessage发送日志
            const logData = {
                type: 'log',
                connectionId: connectionId,
                liveUrl: liveUrl,
                message: logMessage
            };
            const message = this.protoHelper.createCustomMessage(
                logData, this.context
            );
            const buffer = this.protoHelper.serializeClientMessage(message);
            this.sendToServer(buffer);
        };

        // 启动LiveTiktok监控，使用为此连接创建的独立窗口
        connection.liveTiktok.startMonitorRoom(window, liveUrl, eventCallback, logCallback);
        connection.isConnected = true;
        connection.startTime = new Date().getTime();

        // 存储连接
        this.connections.set(connectionId, connection);
        this.globalStats.totalConnections++;
        this.globalStats.activeConnections = this.connections.size;

        console.log(`LiveWebSocketBridge: Started monitoring ${liveUrl} with connection ID: ${connectionId}`);

        return connection;
    }

    // 断开特定连接
    disconnectConnection(connectionId) {
        const connection = this.connections.get(connectionId);
        if (!connection) {
            return false;
        }

        console.log(`LiveWebSocketBridge: Disconnecting connection ${connectionId} (${connection.liveUrl})`);

        // 释放 LiveTiktok 资源
        if (connection.liveTiktok) {
            connection.liveTiktok.release();
        }

        // 关闭对应的窗口
        this.windowManager.closeWindow(connectionId);

        // 从连接池中移除
        this.connections.delete(connectionId);
        this.globalStats.activeConnections = this.connections.size;

        console.log(`LiveWebSocketBridge: Connection ${connectionId} disconnected`);
        return true;
    }

    // 断开所有连接
    disconnectAllConnections() {
        const connectionIds = Array.from(this.connections.keys());
        let disconnectedCount = 0;

        for (const connectionId of connectionIds) {
            if (this.disconnectConnection(connectionId)) {
                disconnectedCount++;
            }
        }
        return disconnectedCount;
    }

    // 更新连接统计信息
    updateConnectionStats(connectionId, eventsData) {
        const connection = this.connections.get(connectionId);
        if (!connection) return;

        connection.stats.totalEvents++;
        connection.stats.lastEventTime = new Date().getTime();

        if (eventsData && eventsData.events_data && Array.isArray(eventsData.events_data)) {
            eventsData.events_data.forEach(event => {
                const eventType = event.msg_type_str || 'unknown';
                connection.stats.eventsByType[eventType] = (connection.stats.eventsByType[eventType] || 0) + 1;
            });
        }
    }

    // 更新全局统计信息
    updateGlobalStats(eventsData) {
        this.globalStats.totalEvents++;
        this.globalStats.lastEventTime = new Date().getTime();

        if (eventsData && eventsData.events_data && Array.isArray(eventsData.events_data)) {
            eventsData.events_data.forEach(event => {
                const eventType = event.msg_type_str || 'unknown';
                this.globalStats.eventsByType[eventType] = (this.globalStats.eventsByType[eventType] || 0) + 1;
            });
        }
    }

    updateHeartBeat(context, totalConnections) {
        const message = this.protoHelper.createHeartbeatMessage(context, totalConnections);
        const buffer = this.protoHelper.serializeClientMessage(message);
        this.sendToServer(buffer);
    }

    // 开始监控直播间并启动WebSocket服务器（保持向后兼容，但现在会创建独立窗口）
    startMonitorRoom(mainWindow, liveUrl, additionalCallback = null, additionalLogCallback = null) {
        // 启动WebSocket服务器
        this.startWebSocketServer();

        // 初始化全局统计信息
        this.globalStats.startTime = new Date().getTime();

        // 如果提供了liveUrl，直接连接（会创建独立窗口，忽略传入的mainWindow）
        if (liveUrl) {
            const connectionId = this.generateConnectionId();
            this.createLiveConnection(connectionId, liveUrl, {
                additionalCallback: additionalCallback,
                additionalLogCallback: additionalLogCallback
            });
            console.log(`LiveWebSocketBridge: Created connection ${connectionId} for ${liveUrl} with independent window`);
        }

        console.log(`LiveWebSocketBridge: WebSocket server available on ${this.serverUrl}`);
        console.log('LiveWebSocketBridge: Ready to accept live room connection requests from clients');
        console.log('LiveWebSocketBridge: Note: Each live room connection will create an independent Electron window');
    }

    // 启动服务（根据模式启动WebSocket服务器或连接到远程服务器）
    startServer() {
        // 初始化全局统计信息
        this.globalStats.startTime = new Date().getTime();

        // 客户端模式：连接到远程服务器
        this.connectToServer();

        console.log(`LiveWebSocketBridge: Client mode started`);
        console.log(`LiveWebSocketBridge: Connecting to remote server: ${this.serverUrl}`);
        console.log('LiveWebSocketBridge: Ready to receive live room connection requests from server');
        console.log('LiveWebSocketBridge: Each live room will create an independent Electron window');
        console.log('LiveWebSocketBridge: Waiting for server commands:');
        console.log('  - live_room_request: Server requests connection to a live room');
        console.log('  - disconnect_room_request: Server requests disconnection from live room(s)');
        console.log('  - get_status_request: Server requests status information');
    }

    // 获取所有连接的统计信息
    getAllConnectionsStats() {
        const connections = {};
        for (const [connectionId, connection] of this.connections) {
            const uptime = connection.startTime ? new Date().getTime() - connection.startTime : 0;
            connections[connectionId] = {
                ...connection.stats,
                liveUrl: connection.liveUrl,
                uptime: uptime,
                isConnected: connection.isConnected
            };
        }
        return connections;
    }

    // 释放资源
    release() {
        console.log('LiveWebSocketBridge: Releasing resources...');

        // 客户端模式：通知服务器即将断开连接
        if (this.isConnected) {
            const message = this.protoHelper.createClientShutdownMessage(
                'LiveWebSocketBridge client is shutting down',
                this.connections.size,
                this.context
            );
            const buffer = this.protoHelper.serializeClientMessage(message);
            this.sendToServer(buffer);
        }

        // 断开与服务器的连接
        this.disconnectFromServer();

        // 断开所有直播间连接
        this.disconnectAllConnections();

        // 关闭所有窗口
        this.windowManager.closeAllWindows();

        console.log('LiveWebSocketBridge: Resources released');
    }

    // 发送自定义消息
    broadcast(data) {
        // 客户端模式：发送给服务器
        const message = this.protoHelper.createCustomMessage(
            data, this.context
        );
        const buffer = this.protoHelper.serializeClientMessage(message);
        this.sendToServer(buffer);
    }

    // 获取WebSocket状态
    getWebSocketStatus() {
        return {
            serverUrl: this.serverUrl,
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            maxReconnectAttempts: this.maxReconnectAttempts
        };
    }
}

module.exports = LiveWebSocketBridge;
